/**
 * Auth Service (Middleware-backed)
 *
 * Implements WebView-based login flow against middleware backend.
 * - Builds the auth URL for WebView
 * - Handles success deep link during WebView navigation
 * - Persists token (and optional user JSON) in SecureStore
 */

import * as SecureStore from 'expo-secure-store';
import Constants from 'expo-constants';
import { Platform } from 'react-native';
import env from '@/config';

const getMiddlewareUrl = () => {
  if (__DEV__) {
    // For Android emulator, use the special host IP
    // For iOS simulator, localhost works
    if (Platform.OS === 'android') {
      return `http://********:8000/auth/login`;
    } else {
      return `http://localhost:8000/auth/login`;
    }
  } else {
    // In production, use your production URL
    return 'https://your-production-middleware.com/auth/login';
  }
};
export interface AuthResult {
  token: string;
  user?: any;
}

export class AuthService {
  private middlewareBaseUrl: string;

  constructor(middlewareBaseUrl: string) {
    this.middlewareBaseUrl = middlewareBaseUrl.replace(/\/$/, '');
  }

  /** Returns the URL to load in a WebView to initiate login */
/** Returns the URL to load in a WebView to initiate login */
getLoginUrl(): string {
  if (__DEV__) {
    // Use mobile-login endpoint for development
    return `${this.middlewareBaseUrl}/auth/mobile-login`;
  } else {
    // Use regular login endpoint for production
    return `${this.middlewareBaseUrl}/auth/login`;
  }
}
  /** Key names for secure storage */
  static TOKEN_KEY = 'auth_token';
  static USER_KEY = 'auth_user';

  /** The deep link we expect on success (from app.config.json) */
  static AUTH_SUCCESS_SCHEME = env.AUTH_REDIRECT_URL;

  /**
   * Handle WebView navigation. If the URL indicates successful auth, persist token and user.
   * Returns whether the URL was handled and, if so, the parsed result.
   */
  async handleWebViewNavigation(url: string): Promise<{ handled: boolean; result?: AuthResult }> {
    try {
      if (!url) return { handled: false };

      if (url.includes(AuthService.AUTH_SUCCESS_SCHEME)) {
        const query = url.split('?')[1] || '';
        const params = new URLSearchParams(query);

        const rawToken = params.get('token');
        const rawUser = params.get('user');

        if (!rawToken) {
          if (__DEV__) console.warn('[AuthService] Success URL missing token');
          return { handled: true };
        }

        let parsedUser: any | undefined = undefined;
        if (rawUser) {
          try {
            // rawUser may be URI encoded
            const decoded = decodeURIComponent(rawUser);
            parsedUser = JSON.parse(decoded);
          } catch (e) {
            if (__DEV__) console.warn('[AuthService] Failed to parse user JSON from callback:', e);
          }
        }

        await SecureStore.setItemAsync(AuthService.TOKEN_KEY, rawToken);
        if (parsedUser !== undefined) {
          await SecureStore.setItemAsync(AuthService.USER_KEY, JSON.stringify(parsedUser));
        }

        if (__DEV__) console.log('[AuthService] Stored auth token and user');
        return { handled: true, result: { token: rawToken, user: parsedUser } };
      }

      return { handled: false };
    } catch (error) {
      if (__DEV__) console.error('[AuthService] handleWebViewNavigation error:', error);
      return { handled: false };
    }
  }

  /** Retrieve the stored auth token */
  async getToken(): Promise<string | null> {
    try {
      return await SecureStore.getItemAsync(AuthService.TOKEN_KEY);
    } catch (e) {
      if (__DEV__) console.warn('[AuthService] getToken error:', e);
      return null;
    }
  }

  /** Retrieve the stored user object */
  async getUser<T = any>(): Promise<T | null> {
    try {
      const s = await SecureStore.getItemAsync(AuthService.USER_KEY);
      return s ? (JSON.parse(s) as T) : null;
    } catch (e) {
      if (__DEV__) console.warn('[AuthService] getUser error:', e);
      return null;
    }
  }

  /** Clear stored auth token and user */
  async logout(): Promise<void> {
    try {
      await SecureStore.deleteItemAsync(AuthService.TOKEN_KEY);
      await SecureStore.deleteItemAsync(AuthService.USER_KEY);
    } catch (e) {
      if (__DEV__) console.warn('[AuthService] logout error:', e);
    }
  }
}


/**
 * Factory: create AuthService using dynamic middleware URL detection.
 * In development, uses the same IP as Metro bundler. In production, uses configured URL.
 */
export const createAuthService = async (): Promise<AuthService> => {
  const authUrl = getMiddlewareUrl();
  // Extract base URL by removing the /auth/login path
  const baseUrl = authUrl.replace('/auth/login', '');
  return new AuthService(baseUrl);
};
